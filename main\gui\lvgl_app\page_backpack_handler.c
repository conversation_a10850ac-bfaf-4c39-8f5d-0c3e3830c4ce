
/**
 * @file page_backpack_handler.c
 * @brief 背包页面处理模块
 *
 * 该模块实现了背包通信相关的UI处理函数，包括频段/通道设置和频率设置
 */

#include "page_backpack_handler.h"
#include "rx5808.h"
#include "rx5808_config.h"
#include "lvgl.h"
#include "esp_log.h"
#include <stdio.h>
#include <stdlib.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/queue.h"
#include "Backpack.h"

#define TAG "BACKPACK_HANDLER"

extern lv_obj_t *lv_channel_label;

QueueHandle_t backpack_msg_queue = NULL;
static void backpack_ui_update_task(void *pvParameters);
static void update_band_channel_ui(void *data);
static void update_frequency_ui(void *data);

backpack_msg_t current_backpack_info = {
    .type = MSG_UPDATE_BACKPACK_INFO,
    .version = "Unknown",
    .version_received = false,
    .status_flags = 0,
    .uid = {0},
    .status_received = false};


const char *page_backpack_get_version(void)
{
    return current_backpack_info.version;
}

bool page_backpack_is_version_received(void)
{
    return current_backpack_info.version_received;
}

uint8_t page_backpack_get_status_flags(void)
{
    return current_backpack_info.status_flags;
}

const uint8_t *page_backpack_get_uid(void)
{
    return current_backpack_info.uid;
}

bool page_backpack_is_status_received(void)
{
    return current_backpack_info.status_received;
}

void page_backpack_update_info(const char *version, bool version_received,
                               uint8_t status_flags, const uint8_t *uid, bool status_received)
{
    // 创建消息
    backpack_msg_t msg;
    msg.type = MSG_UPDATE_BACKPACK_INFO;

    if (version != NULL)
    {
        strncpy(msg.version, version, sizeof(msg.version) - 1);
        msg.version[sizeof(msg.version) - 1] = '\0';
    }

    msg.version_received = version_received;
    msg.status_flags = status_flags;

    if (uid != NULL)
    {
        memcpy(msg.uid, uid, 6);
    }

    msg.status_received = status_received;

    // 发送消息到队列
    if (backpack_msg_queue != NULL)
    {
        if (xQueueSend(backpack_msg_queue, &msg, pdMS_TO_TICKS(100)) != pdTRUE)
        {
            ESP_LOGE(TAG, "Failed to send backpack info update message to queue");
        }
    }
}

/**
 * @brief 初始化背包处理模块
 *
 * 创建消息队列和UI更新任务
 */
void backpack_handler_init(void)
{
    backpack_msg_queue = xQueueCreate(10, sizeof(backpack_msg_t));
    if (backpack_msg_queue == NULL)
    {
        ESP_LOGE(TAG, "Failed to create backpack message queue");
        return;
    }

    xTaskCreatePinnedToCore(backpack_ui_update_task, "backpack_ui", 1536, NULL, 5, NULL, 0);  // Reduced from 2048
}
/**
 * @brief 更新频率标签
 *
 * @param band 频段索引
 * @param channel 通道索引
 */
static void safe_update_frequency_label(uint8_t band, uint8_t channel)
{
    // 更新频段和通道标签
    if (lv_channel_label != NULL)
    {
        // 显示频段和通道信息
        lv_label_set_text_fmt(lv_channel_label, "%c%d",
                              Rx5808_ChxMap[band],
                              channel + 1);

        // ESP_LOGI(TAG, "Setting channel label: %c%d",
        //         Rx5808_ChxMap[band], channel + 1);
    }
    else
    {
        ESP_LOGW(TAG, "Warning: lv_channel_label is NULL");
    }

    // 调用页面主函数中的频率标签更新函数
    extern void fre_label_update(uint8_t a, uint8_t b);
    fre_label_update(band, channel);
}

/**
 * @brief 更新频段和通道UI的回调函数
 *
 * @param data 包含频段和通道信息的数据
 */
static void update_band_channel_ui(void *data)
{
    uint32_t *params = (uint32_t *)data;
    uint8_t band = params[0];
    uint8_t channel = params[1];

    // ESP_LOGI(TAG, "Updating UI for band %d (%c), channel %d (%d)",
    //          band, Rx5808_ChxMap[band], channel, channel + 1);

    // 更新频率标签
    safe_update_frequency_label(band, channel);
}

/**
 * @brief 更新频率UI的回调函数
 *
 * @param data 包含频率信息的数据
 */
static void update_frequency_ui(void *data)
{
    uint16_t frequency = *(uint16_t *)data;

    // 对于自定义频率，显示特殊标记
    if (lv_channel_label != NULL)
    {
        // 显示自定义频率标记
        lv_label_set_text_fmt(lv_channel_label, "Custom");

        // ESP_LOGI(TAG, "Setting custom frequency label");
    }
    // else
    // {
    //     ESP_LOGW(TAG, "Warning: lv_channel_label is NULL");
    // }
}

/**
 * @brief UI更新任务
 *
 * 从消息队列接收消息并在GUI任务中更新UI
 *
 * @param pvParameters 未使用
 */
static void backpack_ui_update_task(void *pvParameters)
{
    backpack_msg_t msg;
    static uint32_t band_channel_params[2];
    static uint16_t frequency_param;

    while (1)
    {
        // 等待消息
        if (xQueueReceive(backpack_msg_queue, &msg, portMAX_DELAY) == pdTRUE)
        {
            // 处理消息
            switch (msg.type)
            {
            case MSG_SET_BAND_CHANNEL:
            {
                uint8_t band = msg.band;
                uint8_t channel = msg.channel;

                // ESP_LOGI(TAG, "UI Task: Setting Band: %d (%c), Channel: %d (%d)",
                //  band, Rx5808_ChxMap[band], channel, channel + 1);

                Chx_count = band;
                channel_count = channel;

                // 获取频率
                uint16_t frequency = Rx5808_Freq[band][channel];

                // 设置RX5808频率
                RX5808_Set_Freq(frequency);

                // 设置RX5808通道索引
                uint8_t channel_index = channel + band * 8;
                Rx5808_Set_Channel(channel_index);

                // 更新接收机配置
                rx5808_div_setup_upload(rx5808_div_config_channel);

                // 准备参数
                band_channel_params[0] = band;
                band_channel_params[1] = channel;

                // 在主任务中更新UI
                lv_res_t res = lv_async_call(update_band_channel_ui, band_channel_params);

                if (res != LV_RES_OK)
                {
                    ESP_LOGE(TAG, "Failed to schedule UI update");
                }
                break;
            }

            case MSG_SET_FREQUENCY:
            {
                uint16_t frequency = msg.frequency;

                // 设置RX5808频率
                RX5808_Set_Freq(frequency);

                // 准备参数
                frequency_param = frequency;

                // 在主任务中更新UI
                lv_res_t res = lv_async_call(update_frequency_ui, &frequency_param);

                if (res != LV_RES_OK)
                {
                    ESP_LOGE(TAG, "Failed to schedule UI update");
                }
                break;
            }

            case MSG_UPDATE_BACKPACK_INFO:
                // 更新当前背包信息
                strncpy(current_backpack_info.version, msg.version, sizeof(current_backpack_info.version));
                current_backpack_info.version_received = msg.version_received;
                current_backpack_info.status_flags = msg.status_flags;
                memcpy(current_backpack_info.uid, msg.uid, 6);
                current_backpack_info.status_received = msg.status_received;
                break;

            default:
                // ESP_LOGW(TAG, "Unknown message type: %d", msg.type);
                break;
            }
        }
    }
}

/**
 * @brief 设置背包频段和通道
 *
 * 根据指定的频段和通道发送消息到队列，由消息处理函数统一处理硬件设置和UI更新
 *
 * @param band 频段索引 (0-5 对应 A-F)
 * @param channel 通道索引 (0-7 对应 1-8)
 */
void page_backpack_set_band_channel(uint8_t band, uint8_t channel)
{
    // 创建消息
    backpack_msg_t msg;
    msg.type = MSG_SET_BAND_CHANNEL;
    msg.band = band;
    msg.channel = channel;

    // 发送消息到队列
    if (backpack_msg_queue != NULL)
    {
        if (xQueueSend(backpack_msg_queue, &msg, pdMS_TO_TICKS(100)) != pdTRUE)
        {
            ESP_LOGE(TAG, "Failed to send band/channel message to queue");
        }
    }
}

/**
 * @brief 设置背包频率
 *
 * 根据指定的频率发送消息到队列，由消息处理函数统一处理硬件设置和UI更新
 *
 * @param frequency 频率值 (MHz)
 * @return 是否成功发送消息
 */
bool backpack_set_frequency(uint16_t frequency)
{
    // 创建消息
    backpack_msg_t msg;
    msg.type = MSG_SET_FREQUENCY;
    msg.frequency = frequency;

    // 发送消息到队列
    if (backpack_msg_queue != NULL)
    {
        if (xQueueSend(backpack_msg_queue, &msg, pdMS_TO_TICKS(100)) == pdTRUE)
        {
            return true;
        }
        else
        {
            // ESP_LOGE(TAG, "Failed to send frequency message to queue");
            return false;
        }
    }
    return false;
}
