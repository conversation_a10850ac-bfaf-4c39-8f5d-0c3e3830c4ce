#if defined(LV_LVGL_H_INCLUDE_SIMPLE)
#include "lvgl.h"
#else
#include "lvgl/lvgl.h"
#endif


#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG_LOCK_IMG
#define LV_ATTRIBUTE_IMG_LOCK_IMG
#endif

#ifndef LV_ATTRIBUTE_IMG_unlock_img
#define LV_ATTRIBUTE_IMG_unlock_img
#endif

// 锁定
const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG_LOCK_IMG uint8_t lock_img_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0x00, 0xff, 0x40, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x40, 0xff, 0x00, 0xff, 
  0x40, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x40, 0xff, 
  0xc5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xc5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x45, 0xff, 0x00, 0xff, 0x00, 0xff, 0x45, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x00, 0xff, 0x00, 0xff, 0x80, 0xff, 0x80, 0xff, 0x00, 0xff, 0x00, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x44, 0xff, 0x00, 0xff, 0xe0, 0xff, 0xe0, 0xff, 0xe0, 0xff, 0xe0, 0xff, 0x00, 0xff, 0x44, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x00, 0xff, 0xa0, 0xff, 0xe0, 0xff, 0xe4, 0xff, 0xe0, 0xff, 0xe0, 0xff, 0xa0, 0xff, 0x00, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x00, 0xff, 0xe0, 0xff, 0xe0, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe0, 0xff, 0xe0, 0xff, 0x00, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x00, 0xff, 0xc0, 0xff, 0xe0, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe4, 0xff, 0xe0, 0xff, 0x00, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x00, 0xff, 0xc4, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe0, 0xff, 0x00, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xa5, 0xff, 0x85, 0xff, 0x85, 0xff, 0x00, 0xff, 0x64, 0xff, 0x85, 0xff, 0x85, 0xff, 0x85, 0xff, 0x85, 0xff, 0x64, 0xff, 0x00, 0xff, 0x85, 0xff, 0x85, 0xff, 0xa5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0xa5, 0xff, 0xa5, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x44, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x44, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x24, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x04, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x04, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x24, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0xc5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 
  0x40, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0x40, 0xff, 
  0x00, 0xff, 0x40, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe5, 0xff, 0xe9, 0xff, 0x40, 0xff, 0x00, 0xff, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0xff, 0x82, 0x48, 0xff, 0x25, 0xe1, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0x25, 0xe1, 0xff, 0x62, 0x40, 0xff, 0x00, 0x00, 0xff, 
  0x62, 0x40, 0xff, 0x05, 0xf9, 0xff, 0xe4, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x05, 0xf9, 0xff, 0x62, 0x40, 0xff, 
  0x05, 0xd1, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0xe5, 0xf8, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x25, 0xd1, 0xff, 
  0x05, 0xf1, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf8, 0xff, 0x46, 0xf1, 0xff, 0x04, 0x41, 0xff, 0x41, 0x00, 0xff, 0x41, 0x00, 0xff, 0x04, 0x41, 0xff, 0x46, 0xf1, 0xff, 0xe4, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x05, 0xf1, 0xff, 
  0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x46, 0xf1, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x20, 0x80, 0xff, 0x21, 0x80, 0xff, 0x20, 0x00, 0xff, 0x00, 0x00, 0xff, 0x46, 0xf1, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0xe3, 0x38, 0xff, 0x00, 0x00, 0xff, 0x20, 0xf8, 0xff, 0x00, 0xf8, 0xff, 0x00, 0xf8, 0xff, 0x41, 0xf8, 0xff, 0x00, 0x00, 0xff, 0xe3, 0x38, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x05, 0xf9, 0xff, 0x00, 0x00, 0xff, 0x41, 0xb0, 0xff, 0x21, 0xf8, 0xff, 0xa3, 0xf0, 0xff, 0x41, 0xf8, 0xff, 0x00, 0xf8, 0xff, 0x41, 0xa8, 0xff, 0x00, 0x00, 0xff, 0x05, 0xf9, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x87, 0xe9, 0xff, 0x00, 0x00, 0xff, 0x20, 0xe0, 0xff, 0x21, 0xf8, 0xff, 0x05, 0xe9, 0xff, 0x05, 0xe9, 0xff, 0x41, 0xf8, 0xff, 0x20, 0xe0, 0xff, 0x00, 0x00, 0xff, 0x87, 0xe9, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x66, 0xe9, 0xff, 0x00, 0x00, 0xff, 0x41, 0xd0, 0xff, 0x62, 0xf8, 0xff, 0xe5, 0xe8, 0xff, 0xe5, 0xf0, 0xff, 0x83, 0xf8, 0xff, 0x00, 0xd8, 0xff, 0x00, 0x00, 0xff, 0x66, 0xe9, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf8, 0xff, 0xe4, 0xf8, 0xff, 0x66, 0xf1, 0xff, 0x00, 0x00, 0xff, 0xc4, 0xc8, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf8, 0xff, 0xc4, 0xf8, 0xff, 0x41, 0xd8, 0xff, 0x00, 0x00, 0xff, 0x66, 0xf1, 0xff, 0xe4, 0xf8, 0xff, 0xe4, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf8, 0xff, 0x25, 0xa1, 0xff, 0x04, 0x79, 0xff, 0x25, 0x79, 0xff, 0x00, 0x00, 0xff, 0xc3, 0x60, 0xff, 0x04, 0x79, 0xff, 0x04, 0x79, 0xff, 0x04, 0x79, 0xff, 0x04, 0x79, 0xff, 0xe4, 0x60, 0xff, 0x00, 0x00, 0xff, 0x25, 0x79, 0xff, 0x04, 0x79, 0xff, 0x25, 0xa1, 0xff, 0xe4, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x00, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x08, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x08, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x25, 0xa1, 0xff, 0x25, 0xa1, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x08, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xc3, 0x50, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf8, 0xff, 0xc3, 0x50, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x08, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x18, 0xff, 0x46, 0xf9, 0xff, 0x46, 0xf9, 0xff, 0x82, 0x18, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x08, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x10, 0xff, 0x82, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x08, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x08, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x82, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x82, 0x08, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 
  0x05, 0xf1, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0xe3, 0x18, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xe3, 0x18, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 
  0x05, 0xd1, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf8, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0x05, 0xf9, 0xff, 0xe5, 0xf8, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x66, 0xd9, 0xff, 
  0x62, 0x40, 0xff, 0x05, 0xf9, 0xff, 0xe4, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0x05, 0xf9, 0xff, 0x62, 0x40, 0xff, 
  0x00, 0x00, 0xff, 0x82, 0x48, 0xff, 0x26, 0xe1, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe5, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xe4, 0xf0, 0xff, 0xa7, 0xf1, 0xff, 0x82, 0x48, 0xff, 0x00, 0x00, 0xff, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0xff, 0x48, 0x82, 0xff, 0xe1, 0x25, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xe1, 0x25, 0xff, 0x40, 0x62, 0xff, 0x00, 0x00, 0xff, 
  0x40, 0x62, 0xff, 0xf9, 0x05, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe4, 0xff, 0xf9, 0x05, 0xff, 0x40, 0x62, 0xff, 
  0xd1, 0x05, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf8, 0xe5, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xd1, 0x25, 0xff, 
  0xf1, 0x05, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe4, 0xff, 0xf1, 0x46, 0xff, 0x41, 0x04, 0xff, 0x00, 0x41, 0xff, 0x00, 0x41, 0xff, 0x41, 0x04, 0xff, 0xf1, 0x46, 0xff, 0xf8, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf1, 0x05, 0xff, 
  0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf1, 0x46, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x80, 0x20, 0xff, 0x80, 0x21, 0xff, 0x00, 0x20, 0xff, 0x00, 0x00, 0xff, 0xf1, 0x46, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x38, 0xe3, 0xff, 0x00, 0x00, 0xff, 0xf8, 0x20, 0xff, 0xf8, 0x00, 0xff, 0xf8, 0x00, 0xff, 0xf8, 0x41, 0xff, 0x00, 0x00, 0xff, 0x38, 0xe3, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf9, 0x05, 0xff, 0x00, 0x00, 0xff, 0xb0, 0x41, 0xff, 0xf8, 0x21, 0xff, 0xf0, 0xa3, 0xff, 0xf8, 0x41, 0xff, 0xf8, 0x00, 0xff, 0xa8, 0x41, 0xff, 0x00, 0x00, 0xff, 0xf9, 0x05, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xe9, 0x87, 0xff, 0x00, 0x00, 0xff, 0xe0, 0x20, 0xff, 0xf8, 0x21, 0xff, 0xe9, 0x05, 0xff, 0xe9, 0x05, 0xff, 0xf8, 0x41, 0xff, 0xe0, 0x20, 0xff, 0x00, 0x00, 0xff, 0xe9, 0x87, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xe9, 0x66, 0xff, 0x00, 0x00, 0xff, 0xd0, 0x41, 0xff, 0xf8, 0x62, 0xff, 0xe8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0x83, 0xff, 0xd8, 0x00, 0xff, 0x00, 0x00, 0xff, 0xe9, 0x66, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe4, 0xff, 0xf8, 0xe4, 0xff, 0xf1, 0x66, 0xff, 0x00, 0x00, 0xff, 0xc8, 0xc4, 0xff, 0xf8, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0xf8, 0xc4, 0xff, 0xd8, 0x41, 0xff, 0x00, 0x00, 0xff, 0xf1, 0x66, 0xff, 0xf8, 0xe4, 0xff, 0xf8, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe4, 0xff, 0xa1, 0x25, 0xff, 0x79, 0x04, 0xff, 0x79, 0x25, 0xff, 0x00, 0x00, 0xff, 0x60, 0xc3, 0xff, 0x79, 0x04, 0xff, 0x79, 0x04, 0xff, 0x79, 0x04, 0xff, 0x79, 0x04, 0xff, 0x60, 0xe4, 0xff, 0x00, 0x00, 0xff, 0x79, 0x25, 0xff, 0x79, 0x04, 0xff, 0xa1, 0x25, 0xff, 0xf8, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x00, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x08, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x08, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x08, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xa1, 0x25, 0xff, 0xa1, 0x25, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x08, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x50, 0xc3, 0xff, 0xf8, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x50, 0xc3, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x08, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x18, 0x82, 0xff, 0xf9, 0x46, 0xff, 0xf9, 0x46, 0xff, 0x18, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x08, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x82, 0xff, 0x10, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x08, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 
  0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x08, 0x82, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x82, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 
  0xf1, 0x05, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0x18, 0xe3, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x18, 0xe3, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 
  0xd1, 0x05, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf8, 0xe5, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf9, 0x05, 0xff, 0xf8, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xd9, 0x66, 0xff, 
  0x40, 0x62, 0xff, 0xf9, 0x05, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe4, 0xff, 0xf9, 0x05, 0xff, 0x40, 0x62, 0xff, 
  0x00, 0x00, 0xff, 0x48, 0x82, 0xff, 0xe1, 0x26, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe5, 0xff, 0xf0, 0xe4, 0xff, 0xf0, 0xe4, 0xff, 0xf1, 0xa7, 0xff, 0x48, 0x82, 0xff, 0x00, 0x00, 0xff, 
#endif
#if LV_COLOR_DEPTH == 32
  0x00, 0x00, 0x00, 0xff, 0x10, 0x0e, 0x47, 0xff, 0x2b, 0x24, 0xdf, 0xff, 0x24, 0x1b, 0xef, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x24, 0x1c, 0xf0, 0xff, 0x29, 0x22, 0xdc, 0xff, 0x0e, 0x0c, 0x43, 0xff, 0x00, 0x00, 0x00, 0xff, 
  0x0e, 0x0c, 0x3e, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x23, 0x1b, 0xef, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x23, 0x1b, 0xf0, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x0f, 0x0d, 0x3f, 0xff, 
  0x28, 0x21, 0xce, 0xff, 0x23, 0x1b, 0xf1, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xf0, 0xff, 0x24, 0x1c, 0xff, 0xff, 0x27, 0x1e, 0xff, 0xff, 0x27, 0x1e, 0xff, 0xff, 0x24, 0x1c, 0xff, 0xff, 0x23, 0x1b, 0xf0, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xf1, 0xff, 0x28, 0x22, 0xcf, 0xff, 
  0x26, 0x1e, 0xf1, 0xff, 0x23, 0x1b, 0xec, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x22, 0x1a, 0xf4, 0xff, 0x30, 0x29, 0xee, 0xff, 0x20, 0x1f, 0x3d, 0xff, 0x07, 0x08, 0x00, 0xff, 0x07, 0x08, 0x00, 0xff, 0x20, 0x1f, 0x3d, 0xff, 0x30, 0x29, 0xee, 0xff, 0x22, 0x1a, 0xf4, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xec, 0xff, 0x26, 0x1e, 0xf1, 0xff, 
  0x23, 0x1b, 0xec, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xf0, 0xff, 0x2f, 0x27, 0xf1, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x03, 0x03, 0x82, 0xff, 0x04, 0x04, 0x82, 0xff, 0x02, 0x03, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x2f, 0x27, 0xf1, 0xff, 0x23, 0x1b, 0xf0, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xec, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x25, 0x1c, 0xff, 0xff, 0x1b, 0x1a, 0x39, 0xff, 0x00, 0x01, 0x00, 0xff, 0x03, 0x03, 0xff, 0xff, 0x00, 0x00, 0xff, 0xff, 0x00, 0x00, 0xff, 0xff, 0x06, 0x06, 0xff, 0xff, 0x01, 0x01, 0x00, 0xff, 0x1b, 0x1a, 0x3a, 0xff, 0x25, 0x1c, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x29, 0x21, 0xff, 0xff, 0x00, 0x00, 0x00, 0xff, 0x06, 0x06, 0xac, 0xff, 0x06, 0x05, 0xff, 0xff, 0x19, 0x13, 0xf2, 0xff, 0x0b, 0x08, 0xf9, 0xff, 0x00, 0x00, 0xff, 0xff, 0x08, 0x08, 0xab, 0xff, 0x00, 0x00, 0x00, 0xff, 0x29, 0x21, 0xff, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x22, 0x1a, 0xf0, 0xff, 0x36, 0x2f, 0xe8, 0xff, 0x00, 0x00, 0x00, 0xff, 0x01, 0x02, 0xdd, 0xff, 0x04, 0x03, 0xff, 0xff, 0x29, 0x20, 0xea, 0xff, 0x29, 0x20, 0xea, 0xff, 0x08, 0x06, 0xff, 0xff, 0x03, 0x03, 0xdd, 0xff, 0x00, 0x00, 0x00, 0xff, 0x36, 0x2f, 0xe8, 0xff, 0x22, 0x1a, 0xf0, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1a, 0xf0, 0xff, 0x30, 0x2a, 0xe5, 0xff, 0x00, 0x00, 0x00, 0xff, 0x07, 0x06, 0xcc, 0xff, 0x11, 0x0d, 0xfd, 0xff, 0x26, 0x1d, 0xeb, 0xff, 0x25, 0x1d, 0xec, 0xff, 0x17, 0x11, 0xfa, 0xff, 0x00, 0x00, 0xdb, 0xff, 0x00, 0x00, 0x00, 0xff, 0x30, 0x2a, 0xe5, 0xff, 0x23, 0x1a, 0xf0, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xf6, 0xff, 0x23, 0x1a, 0xff, 0xff, 0x32, 0x2b, 0xf3, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1e, 0x17, 0xcb, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xfb, 0xff, 0x25, 0x1c, 0xfb, 0xff, 0x21, 0x19, 0xff, 0xff, 0x0a, 0x07, 0xd6, 0xff, 0x00, 0x00, 0x00, 0xff, 0x32, 0x2b, 0xf3, 0xff, 0x23, 0x1a, 0xff, 0xff, 0x23, 0x1b, 0xf6, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xf6, 0xff, 0x28, 0x23, 0xa1, 0xff, 0x21, 0x1e, 0x74, 0xff, 0x28, 0x25, 0x75, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1b, 0x18, 0x61, 0xff, 0x23, 0x1f, 0x7b, 0xff, 0x22, 0x1e, 0x77, 0xff, 0x22, 0x1e, 0x77, 0xff, 0x23, 0x20, 0x7b, 0xff, 0x1d, 0x1a, 0x60, 0xff, 0x00, 0x00, 0x00, 0xff, 0x28, 0x25, 0x75, 0xff, 0x21, 0x1e, 0x74, 0xff, 0x28, 0x23, 0xa1, 0xff, 0x23, 0x1b, 0xf6, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0f, 0x10, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x10, 0x00, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0f, 0x10, 0x08, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x10, 0x08, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x26, 0x22, 0xa3, 0xff, 0x26, 0x22, 0xa3, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1b, 0x19, 0x4f, 0xff, 0x25, 0x1c, 0xff, 0xff, 0x25, 0x1c, 0xff, 0xff, 0x1b, 0x19, 0x4f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x11, 0x11, 0x15, 0xff, 0x2f, 0x26, 0xff, 0xff, 0x2f, 0x26, 0xff, 0xff, 0x11, 0x11, 0x15, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0e, 0x0c, 0xff, 0x0e, 0x0e, 0x0c, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0f, 0x10, 0x09, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 
  0x23, 0x1b, 0xec, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x0e, 0x0f, 0x05, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0f, 0x05, 0xff, 0x26, 0x1d, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xec, 0xff, 
  0x26, 0x1e, 0xf1, 0xff, 0x23, 0x1b, 0xec, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x25, 0x1c, 0xff, 0xff, 0x1b, 0x1b, 0x18, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1b, 0x1b, 0x18, 0xff, 0x25, 0x1c, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xec, 0xff, 0x25, 0x1d, 0xf0, 0xff, 
  0x28, 0x21, 0xce, 0xff, 0x23, 0x1b, 0xf1, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x25, 0x1c, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x25, 0x1c, 0xff, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x22, 0x1a, 0xf0, 0xff, 0x32, 0x2c, 0xda, 0xff, 
  0x0f, 0x0d, 0x3f, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x23, 0x1b, 0xef, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x22, 0x1a, 0xee, 0xff, 0x28, 0x1f, 0xff, 0xff, 0x0e, 0x0c, 0x3d, 0xff, 
  0x00, 0x00, 0x00, 0xff, 0x10, 0x0e, 0x47, 0xff, 0x2c, 0x25, 0xe1, 0xff, 0x24, 0x1b, 0xef, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x24, 0x1c, 0xed, 0xff, 0x23, 0x1b, 0xed, 0xff, 0x22, 0x1a, 0xee, 0xff, 0x39, 0x32, 0xef, 0xff, 0x10, 0x0e, 0x47, 0xff, 0x00, 0x00, 0x00, 0xff, 
#endif
};

// 解锁
const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_LARGE_CONST LV_ATTRIBUTE_IMG_unlock_img uint8_t unlock_img_map[] = {
#if LV_COLOR_DEPTH == 1 || LV_COLOR_DEPTH == 8
  /*Pixel format: Alpha 8 bit, Red: 3 bit, Green: 3 bit, Blue: 2 bit*/
  0x00, 0xff, 0x08, 0xff, 0x35, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x35, 0xff, 0x08, 0xff, 0x00, 0xff, 
  0x04, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x04, 0xff, 
  0x35, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x35, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x29, 0xff, 0x00, 0xff, 0x00, 0xff, 0x29, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x29, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x29, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x2d, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x31, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x29, 0xff, 0x55, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x35, 0xff, 0x00, 0xff, 0x59, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x35, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x35, 0xff, 0x00, 0xff, 0x59, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x31, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x29, 0xff, 0x00, 0xff, 0x2d, 0xff, 0x2d, 0xff, 0x31, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x31, 0xff, 0x31, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x29, 0xff, 0x39, 0xff, 0x39, 0xff, 0x29, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xff, 0x3d, 0xff, 0x3d, 0xff, 0x24, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x24, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x00, 0xff, 0x24, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 
  0x35, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x35, 0xff, 
  0x04, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x04, 0xff, 
  0x00, 0xff, 0x08, 0xff, 0x35, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x39, 0xff, 0x35, 0xff, 0x08, 0xff, 0x00, 0xff, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0xff, 0xa3, 0x09, 0xff, 0x09, 0x25, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0x49, 0x25, 0xff, 0xa3, 0x09, 0xff, 0x00, 0x00, 0xff, 
  0x83, 0x09, 0xff, 0x6b, 0x2e, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0x6b, 0x2e, 0xff, 0x83, 0x09, 0xff, 
  0xe8, 0x24, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0x0a, 0x26, 0xff, 0x2a, 0x2e, 0xff, 0x2a, 0x2e, 0xff, 0x0a, 0x26, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0xa8, 0x24, 0xff, 
  0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0xab, 0x35, 0xff, 0xa5, 0x21, 0xff, 0x00, 0x08, 0xff, 0x00, 0x08, 0xff, 0xa5, 0x21, 0xff, 0xab, 0x35, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0xcb, 0x35, 0xff, 0x00, 0x00, 0xff, 0x00, 0x08, 0xff, 0x85, 0x1a, 0xff, 0x85, 0x1a, 0xff, 0x00, 0x08, 0xff, 0x00, 0x00, 0xff, 0xcb, 0x35, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x0a, 0x26, 0xff, 0x84, 0x19, 0xff, 0x00, 0x08, 0xff, 0x0b, 0x36, 0xff, 0x0a, 0x26, 0xff, 0x0a, 0x26, 0xff, 0x0b, 0x36, 0xff, 0x00, 0x08, 0xff, 0x84, 0x19, 0xff, 0x0a, 0x26, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x89, 0x25, 0xff, 0x0b, 0x2e, 0xff, 0x00, 0x00, 0xff, 0x87, 0x2b, 0xff, 0xea, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xea, 0x25, 0xff, 0xa8, 0x2b, 0xff, 0x00, 0x00, 0xff, 0x0b, 0x2e, 0xff, 0x89, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x89, 0x25, 0xff, 0xab, 0x35, 0xff, 0x65, 0x22, 0xff, 0x4a, 0x35, 0xff, 0xa9, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xca, 0x25, 0xff, 0xa9, 0x2c, 0xff, 0x00, 0x00, 0xff, 0xab, 0x3d, 0xff, 0xa9, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x89, 0x25, 0xff, 0xea, 0x25, 0xff, 0xa9, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xca, 0x25, 0xff, 0x88, 0x1c, 0xff, 0x00, 0x00, 0xff, 0x8b, 0x35, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xca, 0x25, 0xff, 0xea, 0x25, 0xff, 0xea, 0x25, 0xff, 0xea, 0x25, 0xff, 0xea, 0x25, 0xff, 0xea, 0x25, 0xff, 0xea, 0x25, 0xff, 0xea, 0x25, 0xff, 0x0a, 0x26, 0xff, 0xc8, 0x24, 0xff, 0x00, 0x00, 0xff, 0xcb, 0x35, 0xff, 0xea, 0x25, 0xff, 0xca, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xca, 0x25, 0xff, 0xe8, 0x2b, 0xff, 0xe6, 0x22, 0xff, 0x06, 0x23, 0xff, 0x06, 0x23, 0xff, 0x06, 0x23, 0xff, 0x06, 0x23, 0xff, 0x06, 0x23, 0xff, 0x06, 0x23, 0xff, 0x07, 0x23, 0xff, 0x65, 0x1a, 0xff, 0x00, 0x00, 0xff, 0x07, 0x2b, 0xff, 0xe6, 0x22, 0xff, 0xe8, 0x2b, 0xff, 0xca, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x01, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x01, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x62, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x62, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x08, 0x2c, 0xff, 0x08, 0x2c, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x62, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x05, 0x1a, 0xff, 0x4a, 0x26, 0xff, 0x4a, 0x26, 0xff, 0x05, 0x1a, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x62, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xa2, 0x10, 0xff, 0x8c, 0x36, 0xff, 0x8c, 0x36, 0xff, 0xa2, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x62, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x62, 0x10, 0xff, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x62, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x62, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x62, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x2a, 0x2e, 0xff, 0x42, 0x10, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x42, 0x10, 0xff, 0x2a, 0x2e, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 
  0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x0a, 0x26, 0xff, 0xc3, 0x18, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0xc3, 0x18, 0xff, 0x0a, 0x26, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 
  0xa8, 0x24, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x0a, 0x26, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x4b, 0x2e, 0xff, 0x0a, 0x26, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0xa8, 0x24, 0xff, 
  0x83, 0x09, 0xff, 0x6b, 0x2e, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0x6b, 0x2e, 0xff, 0x83, 0x09, 0xff, 
  0x00, 0x00, 0xff, 0xa3, 0x09, 0xff, 0x09, 0x25, 0xff, 0xaa, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0x8a, 0x25, 0xff, 0xaa, 0x25, 0xff, 0x09, 0x25, 0xff, 0xa3, 0x09, 0xff, 0x00, 0x00, 0xff, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0xff, 0x09, 0xa3, 0xff, 0x25, 0x09, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x49, 0xff, 0x09, 0xa3, 0xff, 0x00, 0x00, 0xff, 
  0x09, 0x83, 0xff, 0x2e, 0x6b, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x2e, 0x6b, 0xff, 0x09, 0x83, 0xff, 
  0x24, 0xe8, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x26, 0x0a, 0xff, 0x2e, 0x2a, 0xff, 0x2e, 0x2a, 0xff, 0x26, 0x0a, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x24, 0xa8, 0xff, 
  0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x35, 0xab, 0xff, 0x21, 0xa5, 0xff, 0x08, 0x00, 0xff, 0x08, 0x00, 0xff, 0x21, 0xa5, 0xff, 0x35, 0xab, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x35, 0xcb, 0xff, 0x00, 0x00, 0xff, 0x08, 0x00, 0xff, 0x1a, 0x85, 0xff, 0x1a, 0x85, 0xff, 0x08, 0x00, 0xff, 0x00, 0x00, 0xff, 0x35, 0xcb, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x26, 0x0a, 0xff, 0x19, 0x84, 0xff, 0x08, 0x00, 0xff, 0x36, 0x0b, 0xff, 0x26, 0x0a, 0xff, 0x26, 0x0a, 0xff, 0x36, 0x0b, 0xff, 0x08, 0x00, 0xff, 0x19, 0x84, 0xff, 0x26, 0x0a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x89, 0xff, 0x2e, 0x0b, 0xff, 0x00, 0x00, 0xff, 0x2b, 0x87, 0xff, 0x25, 0xea, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xea, 0xff, 0x2b, 0xa8, 0xff, 0x00, 0x00, 0xff, 0x2e, 0x0b, 0xff, 0x25, 0x89, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x89, 0xff, 0x35, 0xab, 0xff, 0x22, 0x65, 0xff, 0x35, 0x4a, 0xff, 0x25, 0xa9, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xca, 0xff, 0x2c, 0xa9, 0xff, 0x00, 0x00, 0xff, 0x3d, 0xab, 0xff, 0x25, 0xa9, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x89, 0xff, 0x25, 0xea, 0xff, 0x25, 0xa9, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xca, 0xff, 0x1c, 0x88, 0xff, 0x00, 0x00, 0xff, 0x35, 0x8b, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xca, 0xff, 0x25, 0xea, 0xff, 0x25, 0xea, 0xff, 0x25, 0xea, 0xff, 0x25, 0xea, 0xff, 0x25, 0xea, 0xff, 0x25, 0xea, 0xff, 0x25, 0xea, 0xff, 0x26, 0x0a, 0xff, 0x24, 0xc8, 0xff, 0x00, 0x00, 0xff, 0x35, 0xcb, 0xff, 0x25, 0xea, 0xff, 0x25, 0xca, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xca, 0xff, 0x2b, 0xe8, 0xff, 0x22, 0xe6, 0xff, 0x23, 0x06, 0xff, 0x23, 0x06, 0xff, 0x23, 0x06, 0xff, 0x23, 0x06, 0xff, 0x23, 0x06, 0xff, 0x23, 0x06, 0xff, 0x23, 0x07, 0xff, 0x1a, 0x65, 0xff, 0x00, 0x00, 0xff, 0x2b, 0x07, 0xff, 0x22, 0xe6, 0xff, 0x2b, 0xe8, 0xff, 0x25, 0xca, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x01, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x01, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x62, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x62, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x2c, 0x08, 0xff, 0x2c, 0x08, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x62, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x1a, 0x05, 0xff, 0x26, 0x4a, 0xff, 0x26, 0x4a, 0xff, 0x1a, 0x05, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x62, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0xa2, 0xff, 0x36, 0x8c, 0xff, 0x36, 0x8c, 0xff, 0x10, 0xa2, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x62, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x62, 0xff, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x62, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x62, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x62, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x2e, 0x2a, 0xff, 0x10, 0x42, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x10, 0x42, 0xff, 0x2e, 0x2a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 
  0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x26, 0x0a, 0xff, 0x18, 0xc3, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x18, 0xc3, 0xff, 0x26, 0x0a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 
  0x24, 0xa8, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x26, 0x0a, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x2e, 0x4b, 0xff, 0x26, 0x0a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x24, 0xa8, 0xff, 
  0x09, 0x83, 0xff, 0x2e, 0x6b, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x2e, 0x6b, 0xff, 0x09, 0x83, 0xff, 
  0x00, 0x00, 0xff, 0x09, 0xa3, 0xff, 0x25, 0x09, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0x8a, 0xff, 0x25, 0xaa, 0xff, 0x25, 0x09, 0xff, 0x09, 0xa3, 0xff, 0x00, 0x00, 0xff, 
#endif
#if LV_COLOR_DEPTH == 32
  0x00, 0x00, 0x00, 0xff, 0x17, 0x35, 0x0a, 0xff, 0x45, 0xa0, 0x1e, 0xff, 0x4d, 0xb3, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb3, 0x22, 0xff, 0x47, 0xa7, 0x20, 0xff, 0x16, 0x35, 0x0a, 0xff, 0x00, 0x00, 0x00, 0xff, 
  0x14, 0x2e, 0x09, 0xff, 0x57, 0xcc, 0x27, 0xff, 0x4d, 0xb3, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb3, 0x22, 0xff, 0x57, 0xcc, 0x27, 0xff, 0x14, 0x2f, 0x09, 0xff, 
  0x42, 0x9b, 0x1d, 0xff, 0x4d, 0xb4, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb3, 0x21, 0xff, 0x50, 0xbe, 0x22, 0xff, 0x53, 0xc5, 0x24, 0xff, 0x53, 0xc5, 0x24, 0xff, 0x50, 0xbe, 0x22, 0xff, 0x4c, 0xb3, 0x21, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4d, 0xb4, 0x22, 0xff, 0x40, 0x95, 0x1c, 0xff, 
  0x4d, 0xb4, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb5, 0x20, 0xff, 0x56, 0xb5, 0x2e, 0xff, 0x25, 0x35, 0x1f, 0xff, 0x00, 0x00, 0x04, 0xff, 0x00, 0x00, 0x04, 0xff, 0x25, 0x35, 0x1f, 0xff, 0x56, 0xb5, 0x2e, 0xff, 0x4c, 0xb5, 0x20, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb3, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb3, 0x21, 0xff, 0x55, 0xb7, 0x2d, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x06, 0xff, 0x2b, 0x51, 0x1b, 0xff, 0x2b, 0x51, 0x1b, 0xff, 0x00, 0x00, 0x06, 0xff, 0x00, 0x00, 0x00, 0xff, 0x55, 0xb7, 0x2d, 0xff, 0x4c, 0xb3, 0x21, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x50, 0xbf, 0x22, 0xff, 0x21, 0x31, 0x1a, 0xff, 0x00, 0x00, 0x04, 0xff, 0x5a, 0xc1, 0x2f, 0xff, 0x50, 0xbf, 0x22, 0xff, 0x50, 0xbf, 0x22, 0xff, 0x5a, 0xc1, 0x2f, 0xff, 0x00, 0x00, 0x04, 0xff, 0x21, 0x31, 0x1a, 0xff, 0x50, 0xbf, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4b, 0xb1, 0x21, 0xff, 0x54, 0xbf, 0x28, 0xff, 0x00, 0x00, 0x00, 0xff, 0x3a, 0x6f, 0x24, 0xff, 0x4e, 0xbb, 0x21, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4e, 0xbb, 0x21, 0xff, 0x3c, 0x72, 0x25, 0xff, 0x00, 0x00, 0x00, 0xff, 0x54, 0xc0, 0x27, 0xff, 0x4b, 0xb1, 0x21, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4b, 0xb1, 0x20, 0xff, 0x55, 0xb3, 0x2f, 0xff, 0x2b, 0x4b, 0x1d, 0xff, 0x53, 0xa7, 0x30, 0xff, 0x4b, 0xb3, 0x20, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4d, 0xb6, 0x22, 0xff, 0x46, 0x94, 0x26, 0xff, 0x00, 0x00, 0x00, 0xff, 0x5a, 0xb3, 0x35, 0xff, 0x4b, 0xb2, 0x20, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4b, 0xb1, 0x20, 0xff, 0x4f, 0xbb, 0x22, 0xff, 0x4b, 0xb3, 0x20, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4e, 0xb6, 0x23, 0xff, 0x3d, 0x90, 0x1b, 0xff, 0x00, 0x00, 0x00, 0xff, 0x55, 0xb0, 0x2f, 0xff, 0x4c, 0xb3, 0x21, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4d, 0xb7, 0x21, 0xff, 0x4f, 0xbb, 0x22, 0xff, 0x4f, 0xbb, 0x22, 0xff, 0x4f, 0xbb, 0x22, 0xff, 0x4f, 0xbb, 0x22, 0xff, 0x4f, 0xbb, 0x22, 0xff, 0x4f, 0xbb, 0x22, 0xff, 0x4f, 0xbb, 0x22, 0xff, 0x51, 0xc0, 0x23, 0xff, 0x40, 0x98, 0x1c, 0xff, 0x00, 0x00, 0x00, 0xff, 0x59, 0xb9, 0x31, 0xff, 0x4f, 0xbd, 0x21, 0xff, 0x4d, 0xb7, 0x21, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4d, 0xb7, 0x21, 0xff, 0x40, 0x7d, 0x27, 0xff, 0x31, 0x5a, 0x20, 0xff, 0x33, 0x5e, 0x21, 0xff, 0x33, 0x5e, 0x21, 0xff, 0x33, 0x5e, 0x21, 0xff, 0x33, 0x5e, 0x21, 0xff, 0x33, 0x5e, 0x21, 0xff, 0x33, 0x5e, 0x21, 0xff, 0x34, 0x61, 0x22, 0xff, 0x29, 0x4c, 0x1b, 0xff, 0x00, 0x00, 0x00, 0xff, 0x37, 0x5e, 0x27, 0xff, 0x31, 0x5b, 0x20, 0xff, 0x40, 0x7d, 0x27, 0xff, 0x4d, 0xb7, 0x21, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x52, 0xc3, 0x24, 0xff, 0x0b, 0x00, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0b, 0x00, 0x0f, 0xff, 0x52, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x3f, 0x7e, 0x25, 0xff, 0x3f, 0x7e, 0x25, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x26, 0x40, 0x1b, 0xff, 0x53, 0xc8, 0x23, 0xff, 0x53, 0xc8, 0x23, 0xff, 0x26, 0x40, 0x1b, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x12, 0x14, 0x11, 0xff, 0x5d, 0xd1, 0x2d, 0xff, 0x5d, 0xd1, 0x2c, 0xff, 0x12, 0x14, 0x11, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0d, 0x0e, 0xff, 0x0e, 0x0d, 0x0e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0e, 0x0a, 0x0f, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x0c, 0x07, 0x0e, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x0c, 0x07, 0x0e, 0xff, 0x53, 0xc3, 0x24, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 
  0x4c, 0xb2, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x51, 0xc1, 0x23, 0xff, 0x1a, 0x19, 0x1b, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x00, 0x00, 0x00, 0xff, 0x1a, 0x19, 0x1b, 0xff, 0x51, 0xc1, 0x23, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4d, 0xb3, 0x22, 0xff, 
  0x40, 0x95, 0x1c, 0xff, 0x4d, 0xb4, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x51, 0xc1, 0x23, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x55, 0xc7, 0x26, 0xff, 0x51, 0xc1, 0x23, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4d, 0xb4, 0x22, 0xff, 0x40, 0x95, 0x1c, 0xff, 
  0x14, 0x2f, 0x09, 0xff, 0x57, 0xcc, 0x27, 0xff, 0x4d, 0xb3, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4d, 0xb3, 0x22, 0xff, 0x57, 0xcc, 0x27, 0xff, 0x14, 0x2f, 0x09, 0xff, 
  0x00, 0x00, 0x00, 0xff, 0x17, 0x35, 0x0a, 0xff, 0x44, 0xa0, 0x1f, 0xff, 0x4d, 0xb3, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4c, 0xb1, 0x22, 0xff, 0x4d, 0xb3, 0x22, 0xff, 0x44, 0xa0, 0x1f, 0xff, 0x17, 0x35, 0x0a, 0xff, 0x00, 0x00, 0x00, 0xff, 
#endif
};

// 锁定
const lv_img_dsc_t lock_img = {
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .header.always_zero = 0,
  .header.reserved = 0,
  .header.w = 22,
  .header.h = 24,
  .data_size = 528 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .data = lock_img_map,
};

// 解锁
const lv_img_dsc_t unlock_img = {
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .header.always_zero = 0,
  .header.reserved = 0,
  .header.w = 22,
  .header.h = 24,
  .data_size = 528 * LV_IMG_PX_SIZE_ALPHA_BYTE,
  .data = unlock_img_map,
};