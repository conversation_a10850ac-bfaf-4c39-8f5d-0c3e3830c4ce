/**
 * @file Backpack.c
 * @brief 背包通信模块实现
 */

#include "lcd.h"
#include "beep.h"
#include "SPI.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "driver/ledc.h"
#include "freertos/semphr.h"
#include "driver/uart.h"
#include <string.h>
#include "hwvers.h"
#include "esp_log.h"
#include "rx5808.h"
#include "rx5808_config.h"
#include "Backpack.h"
#include "page_backpack_handler.h"
#include "msp.h"

static uint8_t last_set_band = 0;
static uint8_t last_set_channel = 0;
static uint16_t last_set_frequency = 0;
static uint32_t last_command_time = 0;
static uint32_t packet_count = 0;

static void backpack_rx_task(void *param);
static void backpack_init_task(void *param);
static void parse_msp_packet(msp_packet_t *packet);
static void map_backpack_to_project_band_channel(uint8_t backpack_band, uint8_t backpack_channel,
												 uint8_t *project_band, uint8_t *project_channel);
static int find_backpack_band_index_by_char(char band_char);

/**
 * @brief 将背包频段/通道映射到项目频段/通道
 */
static void map_backpack_to_project_band_channel(uint8_t backpack_band, uint8_t backpack_channel,
												 uint8_t *project_band, uint8_t *project_channel)
{

	*project_band = backpack_band;
	*project_channel = backpack_channel;
}

/**
 * @brief 根据频段字符查找背包频段索引
 */
static int find_backpack_band_index_by_char(char band_char)
{
	for (int i = 0; i < Rx5808_ChxMap_sum; i++)
	{
		if (Rx5808_ChxMap[i] == band_char)
		{
			return i;
		}
	}
	return -1;
}

/**
 * @brief 初始化背包通信
 */
void Backpack_init(void)
{
	uart_config_t uart1_config = {
		.baud_rate = 115200,
		.data_bits = UART_DATA_8_BITS,
		.parity = UART_PARITY_DISABLE,
		.stop_bits = UART_STOP_BITS_1,
		.flow_ctrl = UART_HW_FLOWCTRL_DISABLE,
		.rx_flow_ctrl_thresh = 0,
		.source_clk = UART_SCLK_APB,
	};

	ESP_ERROR_CHECK(uart_param_config(UART_NUM_1, &uart1_config));
	ESP_ERROR_CHECK(uart_set_pin(UART_NUM_1, Backpack_U1TxD, Backpack_U1RxD, UART_PIN_NO_CHANGE, UART_PIN_NO_CHANGE));
	ESP_ERROR_CHECK(uart_driver_install(UART_NUM_1, RX_BUF_SIZE * 2, TX_BUF_SIZE * 2, 0, NULL, 0));

	// 初始化MSP协议
	MSP_init(parse_msp_packet);
	MSP_set_send_function(Backpack_send_data);

	xTaskCreate(backpack_rx_task, "Backpack_rx_task", 1024, NULL, 5, NULL);
	xTaskCreate(backpack_init_task, "backpack_init", 1024, NULL, 5, NULL);  
}

/**
 * @brief 发送数据到背包
 */
void Backpack_send_data(uint8_t *data, int len)
{
	uart_write_bytes(UART_NUM_1, (const char *)data, len);
}

/**
 * @brief 解析MSP数据包
 */
static void parse_msp_packet(msp_packet_t *packet)
{
	packet_count++;
	uint32_t current_time = xTaskGetTickCount() * portTICK_PERIOD_MS;

	// 处理版本响应
	if (packet->function == MSP_ELRS_BACKPACK_GET_VERSION &&
		packet->type == MSP_V2_TYPE_OUT)
	{
		char version_str[RX_BUF_SIZE + 1] = {0};
		memcpy(version_str, packet->payload, packet->payload_size);
		version_str[packet->payload_size] = '\0';

		// 更新UI层的背包信息
		page_backpack_update_info(version_str, true,
								  page_backpack_get_status_flags(),
								  page_backpack_get_uid(),
								  page_backpack_is_status_received());
	}
	// 处理状态响应
	else if (packet->function == MSP_ELRS_BACKPACK_GET_STATUS &&
			 packet->type == MSP_V2_TYPE_OUT &&
			 packet->payload_size >= 7)
	{
		uint8_t status_flags = packet->payload[0];
		uint8_t uid[6];
		memcpy(uid, &packet->payload[1], 6);

		// 更新UI层的背包信息
		page_backpack_update_info(page_backpack_get_version(),
								  page_backpack_is_version_received(),
								  status_flags, uid, true);
	}

	// 处理频道索引命令
	if (packet->function == MSP_ELRS_BACKPACK_SET_CHANNEL_INDEX &&
		packet->payload_size >= 1)
	{
		uint8_t channel_index = packet->payload[0];
		uint8_t backpack_band = channel_index / 8;
		uint8_t backpack_channel = channel_index % 8;

		if (backpack_band < 10)
		{
			uint8_t project_band, project_channel;
			map_backpack_to_project_band_channel(backpack_band, backpack_channel,
												 &project_band, &project_channel);

			if (project_band < Rx5808_ChxMap_sum && project_channel < 8)
			{
				uint16_t frequency = Rx5808_Freq[project_band][project_channel];

				last_set_band = project_band;
				last_set_channel = project_channel;
				last_set_frequency = frequency;
				last_command_time = current_time;

				// 更新UI
				page_backpack_set_band_channel(project_band, project_channel);
			}
		}
		MSP_send_response(MSP_ELRS_BACKPACK_SET_CHANNEL_INDEX, &channel_index, 1);
	}
	// 处理获取频道索引命令
	else if (packet->function == MSP_ELRS_BACKPACK_GET_CHANNEL_INDEX)
	{
		uint8_t current_band = Chx_count;
		uint8_t current_channel = channel_count;
		char current_band_char = Rx5808_ChxMap[current_band];
		int backpack_band = find_backpack_band_index_by_char(current_band_char);

		if (backpack_band < 0)
		{
			backpack_band = 0;
		}

		uint8_t channel_index = backpack_band * 8 + current_channel;
		MSP_send_response(MSP_ELRS_BACKPACK_GET_CHANNEL_INDEX, &channel_index, 1);
	}
}

/**
 * @brief 背包接收任务
 */
static void backpack_rx_task(void *param)
{
	uint8_t data[32];
	int len;

	while (1)
	{
		len = uart_read_bytes(UART_NUM_1, data, sizeof(data), 10 / portTICK_PERIOD_MS);

		if (len > 0)
		{
			for (int i = 0; i < len; i++)
			{

				MSP_process_byte(data[i]);
			}
		}

		vTaskDelay(20 / portTICK_PERIOD_MS);
	}
}

/**
 * @brief 背包初始化任务
 */
static void backpack_init_task(void *param)
{
	vTaskDelay(2000 / portTICK_PERIOD_MS);

	MSP_send_command(MSP_ELRS_BACKPACK_GET_VERSION, NULL, 0);
	vTaskDelay(100 / portTICK_PERIOD_MS);
	MSP_send_command(MSP_ELRS_BACKPACK_GET_STATUS, NULL, 0);

	vTaskDelete(NULL);
}

/**
 * @brief 获取背包版本信息
 */
void backpack_get_version(void)
{
	MSP_send_command(MSP_ELRS_BACKPACK_GET_VERSION, NULL, 0);
}

/**
 * @brief 获取背包状态信息
 */
void backpack_get_status(void)
{
	MSP_send_command(MSP_ELRS_BACKPACK_GET_STATUS, NULL, 0);
}

/**
 * @brief 获取背包版本字符串
 */
const char *backpack_get_version_string(void)
{
	return page_backpack_get_version();
}

/**
 * @brief 检查背包版本是否已接收
 */
bool backpack_is_version_received(void)
{
	return page_backpack_is_version_received();
}

/**
 * @brief 获取背包状态标志
 */
uint8_t backpack_get_status_flags(void)
{
	return page_backpack_get_status_flags();
}

/**
 * @brief 获取背包UID
 */
const uint8_t *backpack_get_uid(void)
{
	return page_backpack_get_uid();
}

/**
 * @brief 检查背包状态是否已接收
 */
bool backpack_is_status_received(void)
{
	return page_backpack_is_status_received();
}
