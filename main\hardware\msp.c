/**
 * @file msp.c
 * @brief MSP协议模块实现
 */

#include "msp.h"
#include "esp_log.h"
#include <string.h>

static msp_state_e msp_state = MSP_IDLE;
static msp_packet_t msp_packet;
static uint16_t msp_offset = 0;
static uint8_t msp_crc = 0;
static uint8_t msp_input_buffer[RX_BUF_SIZE];
static uint16_t msp_input_pos = 0;

static msp_callback_t packet_callback = NULL;

static void (*send_data_func)(uint8_t *data, int len) = NULL;

/**
 * @brief 设置发送数据的函数
 */
void MSP_set_send_function(void (*func)(uint8_t *data, int len))
{
    send_data_func = func;
}

/**
 * @brief 初始化MSP协议处理
 */
void MSP_init(msp_callback_t callback)
{
    msp_state = MSP_IDLE;
    packet_callback = callback;
}

/**
 * @brief DVB-S2标准
 */
uint8_t MSP_crc8_dvb_s2(uint8_t crc, uint8_t a)
{
    crc ^= a;
    for (int i = 0; i < 8; ++i)
    {
        if (crc & 0x80)
        {
            crc = (crc << 1) ^ 0xD5;
        }
        else
        {
            crc = crc << 1;
        }
    }
    return crc;
}

/**
 * @brief 处理接收到的字节，实现MSP协议状态机
 */
bool MSP_process_byte(uint8_t c)
{
    if (msp_input_pos < RX_BUF_SIZE)
    {
        msp_input_buffer[msp_input_pos++] = c;
    }

    switch (msp_state)
    {
    case MSP_IDLE:
        if (c == MSP_V2_FRAME_START)
        {
            msp_state = MSP_HEADER_START;
            msp_packet.type = 0;
            msp_packet.flags = 0;
            msp_packet.function = 0;
            msp_packet.payload_size = 0;
            msp_packet.crc = 0;
            msp_input_pos = 1;
        }
        break;

    case MSP_HEADER_START:
        if (c == MSP_V2_MARKER)
        {
            msp_state = MSP_HEADER_X;
        }
        else
        {
            msp_state = MSP_IDLE;
        }
        break;

    case MSP_HEADER_X:
        msp_state = MSP_HEADER_TYPE;
        msp_offset = 0;
        msp_crc = 0;

        if (c == MSP_V2_TYPE_IN)
        {
            msp_packet.type = MSP_V2_TYPE_IN;
        }
        else if (c == MSP_V2_TYPE_OUT)
        {
            msp_packet.type = MSP_V2_TYPE_OUT;
        }
        else if (c == MSP_V2_TYPE_ERR)
        {
            msp_packet.type = MSP_V2_TYPE_ERR;
        }
        else
        {
            msp_state = MSP_IDLE;
        }
        break;

    case MSP_HEADER_TYPE:
        msp_packet.flags = c;
        msp_crc = MSP_crc8_dvb_s2(0, c);
        msp_state = MSP_HEADER_FLAG;
        break;

    case MSP_HEADER_FLAG:
        msp_packet.function = c;
        msp_crc = MSP_crc8_dvb_s2(msp_crc, c);
        msp_state = MSP_HEADER_FUNCTION_LO;
        break;

    case MSP_HEADER_FUNCTION_LO:
        msp_packet.function |= (c << 8);
        msp_crc = MSP_crc8_dvb_s2(msp_crc, c);
        msp_state = MSP_HEADER_FUNCTION_HI;
        break;

    case MSP_HEADER_FUNCTION_HI:
        msp_packet.payload_size = c;
        msp_crc = MSP_crc8_dvb_s2(msp_crc, c);
        msp_state = MSP_HEADER_SIZE_LO;
        break;

    case MSP_HEADER_SIZE_LO:
        msp_packet.payload_size |= (c << 8);
        msp_crc = MSP_crc8_dvb_s2(msp_crc, c);
        msp_offset = 0;

        if (msp_packet.payload_size == 0)
        {
            msp_state = MSP_CHECKSUM;
        }
        else
        {
            msp_state = MSP_PAYLOAD;
        }
        break;

    case MSP_PAYLOAD:
        msp_packet.payload[msp_offset++] = c;
        msp_crc = MSP_crc8_dvb_s2(msp_crc, c);

        if (msp_offset >= msp_packet.payload_size)
        {
            msp_state = MSP_CHECKSUM;
        }
        break;

    case MSP_CHECKSUM:
        msp_packet.crc = c;
        if (msp_crc == c)
        {
            msp_state = MSP_COMMAND_RECEIVED;
        }
        else
        {
            msp_state = MSP_IDLE;
        }
        break;

    default:
        msp_state = MSP_IDLE;
        break;
    }

    if (msp_state == MSP_COMMAND_RECEIVED)
    {
        msp_state = MSP_IDLE;
        if (packet_callback)
        {
            packet_callback(&msp_packet);
        }
        return true;
    }
    return false;
}

/**
 * @brief 处理完整的MSP数据包
 */
void MSP_parse_completed_packet(void)
{
    if (packet_callback)
    {
        packet_callback(&msp_packet);
    }
}

/**
 * @brief 发送MSP数据包（通用函数）
 */
static void MSP_send_packet(uint8_t type, uint16_t function, uint8_t *payload, uint16_t payload_size)
{
    if (send_data_func == NULL)
    {
        return;
    }

    uint8_t buffer[9 + TX_BUF_SIZE];
    uint16_t pos = 0;
    uint8_t crc = 0;

    buffer[pos++] = MSP_V2_FRAME_START;
    buffer[pos++] = MSP_V2_MARKER;
    buffer[pos++] = type;

    buffer[pos++] = 0;
    crc = MSP_crc8_dvb_s2(0, buffer[3]);

    buffer[pos++] = function & 0xFF;
    crc = MSP_crc8_dvb_s2(crc, buffer[4]);

    buffer[pos++] = (function >> 8) & 0xFF;
    crc = MSP_crc8_dvb_s2(crc, buffer[5]);

    buffer[pos++] = payload_size & 0xFF;
    crc = MSP_crc8_dvb_s2(crc, buffer[6]);

    buffer[pos++] = (payload_size >> 8) & 0xFF;
    crc = MSP_crc8_dvb_s2(crc, buffer[7]);

    for (uint16_t i = 0; i < payload_size; i++)
    {
        buffer[pos++] = payload[i];
        crc = MSP_crc8_dvb_s2(crc, payload[i]);
    }

    buffer[pos++] = crc;
    send_data_func(buffer, pos);
}

/**
 * @brief 发送MSP响应数据包
 */
void MSP_send_response(uint16_t function, uint8_t *payload, uint16_t payload_size)
{
    MSP_send_packet(MSP_V2_TYPE_OUT, function, payload, payload_size);
}

/**
 * @brief 发送MSP命令
 */
void MSP_send_command(uint16_t function, uint8_t *payload, uint16_t payload_size)
{
    MSP_send_packet(MSP_V2_TYPE_IN, function, payload, payload_size);
}
