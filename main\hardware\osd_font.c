
#include "capi_video.h"
#include <string.h>
#include "osd_font.h"

/*
    绘制单个汉字
    外层循环 for (int i = 0; i < 32; i++)：这个循环用于遍历 32 行点阵数据。
    中层循环 for (int j = 0; j < 2; j++)：因为每行数据由 2 个字节表示，所以这个循环用于处理每行的 2 个字节。
    内层循环 for (int bit = 0; bit < 8; bit++)：这个循环用于逐位检查每个字节的 8 位。
    判断条件 if (byte_data & (1 << bit))：如果当前位为 1，则表示该像素点需要绘制。
    计算像素位置 int px = x + j * 8 + bit; 和 int py = y + i;：根据当前字节和位的索引计算像素点的位置。
    调用 esp32_video_set_color 函数：设置指定位置的像素颜色。
*/

// uint8_t color = 255;
#define b_color 0
#define W_color 255
const uint8_t hanzi_font[][64];
const uint8_t letter_font[26][16];

void draw_hanzi(uint8_t x, uint8_t y, uint8_t index ) {
    for (uint8_t i = 0; i < 32; i++) {
        for (uint8_t j = 0; j < 2; j++) {
            uint8_t byte_data = hanzi_font[index][i * 2 + j];
            for (uint8_t bit = 0; bit < 8; bit++) {
                if (byte_data & (1 << bit)) {
                    uint8_t px = x + j * 8 + bit;
                    uint8_t py = y + i;
                    esp32_video_set_color(px, py, W_color);
                }
            }
        }
    }
}

// 显示单个英文字母
void draw_letter(int x, int y, char letter) {
    if (letter >= 'A' && letter <= 'Z') {
        int index = letter - 'A';
        for (int i = 0; i < 16; i++) {
            uint8_t byte_data = letter_font[index][i];
            for (int bit = 0; bit < 16; bit++) {
                if (byte_data & (1 << bit)) {
                    int px = x + bit;
                    int py = y + i;
                    esp32_video_set_color(px, py, W_color);
                }
            }
        }
    }
}

// 显示英文字符串
void draw_string(int x, int y, const char* str ) {
    int current_x = x;
    while (*str) {
        draw_letter(current_x, y, *str);
        // 在绘制完一个字母后将绘制位置向右移动 8 个像素。
        current_x += 8; // 每个字母宽度为 8 像素
        str++;
    }
}

// 清除操作提示信息所在的整片区域
void clear_operation_tip_area(uint8_t start_x, uint8_t start_y, uint8_t width_cont ,uint8_t height_cont) 
{
    uint16_t width = 32 * width_cont;  // 4 个汉字，每个汉字 32 像素宽
    uint16_t height = 32 * height_cont;

    for (uint16_t x = start_x; x < width; x++) {
        for (uint16_t y = start_y; y < height; y++) {
            esp32_video_set_color( x,  y, b_color);
        }
    }
}

// 绘制操作提示信息
void draw_operation_tip_video() {

    draw_hanzi(10, 10, 0 );   // 长
    draw_hanzi(26, 10, 1 );  // 按
    draw_hanzi(42, 10, 2 );  // 解
    draw_hanzi(58, 10, 3 );  // 锁

    // 显示字符串 "HELLO"
    draw_string(10, 0, "HELLO");
}

/*
    16x16 汉字点阵数据
    Pctolcd2002 ，阴码，逆向，逐行式，C51格式，16*16
*/
const uint8_t hanzi_font[][64] = {
/*
    向上下左右确认返回长短按调通频道一级参翻页进入菜单锁屏
    向(0) 上(1) 下(2) 左(3) 右(4) 确(5) 认(6) 返(7) 回(8) 长(9) 短(10) 
    按(11) 调(12) 通(13) 频(14) 道(15) 一(16) 级(17) 参(18)翻(19) 页(20) 
    进(21) 入(22) 菜(23) 单(24) 锁(25) 屏(26) 空白(27)*/

{0x40,0x00,0x20,0x00,0x10,0x00,0xFE,0x3F,0x02,0x20,0x02,0x20,0xE2,0x23,0x22,0x22,
0x22,0x22,0x22,0x22,0x22,0x22,0xE2,0x23,0x22,0x22,0x02,0x20,0x02,0x28,0x02,0x10},/*"向",0*/

{0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0xC0,0x1F,0x40,0x00,
0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0xFF,0x7F,0x00,0x00},/*"上",1*/

{0x00,0x00,0xFF,0x7F,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x02,0x40,0x04,
0x40,0x08,0x40,0x10,0x40,0x10,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00,0x40,0x00},/*"下",2*/

{0x40,0x00,0x40,0x00,0x40,0x00,0xFF,0x7F,0x20,0x00,0x20,0x00,0x20,0x00,0x10,0x00,
0xF0,0x1F,0x08,0x01,0x08,0x01,0x04,0x01,0x02,0x01,0x01,0x01,0xFC,0x7F,0x00,0x00},/*"左",3*/

{0x40,0x00,0x40,0x00,0x40,0x00,0xFF,0x7F,0x20,0x00,0x20,0x00,0x10,0x00,0x10,0x00,
0xF8,0x1F,0x14,0x10,0x12,0x10,0x11,0x10,0x10,0x10,0x10,0x10,0xF0,0x1F,0x10,0x10},/*"右",4*/

{0x00,0x02,0x00,0x02,0x3F,0x3E,0x08,0x21,0x88,0x10,0x44,0x7F,0x3C,0x49,0x26,0x49,
0x26,0x7F,0x25,0x49,0x24,0x49,0x24,0x7F,0x3C,0x49,0xA4,0x48,0x84,0x50,0x40,0x20},/*"确",5*/

{0x00,0x02,0x04,0x02,0x08,0x02,0x08,0x02,0x00,0x02,0x00,0x02,0x0F,0x02,0x08,0x02,
0x08,0x05,0x08,0x05,0x08,0x05,0xA8,0x08,0x98,0x08,0x48,0x10,0x20,0x20,0x10,0x40},/*"认",6*/

{0x00,0x10,0x04,0x3C,0xC8,0x03,0x48,0x00,0x40,0x00,0xC0,0x3F,0x4F,0x20,0x48,0x11,
0x48,0x0A,0x48,0x04,0x48,0x0A,0x28,0x11,0xA8,0x20,0x14,0x00,0xE2,0x7F,0x00,0x00},/*"返",7*/

{0x00,0x00,0xFC,0x1F,0x04,0x10,0x04,0x10,0xE4,0x13,0x24,0x12,0x24,0x12,0x24,0x12,
0x24,0x12,0x24,0x12,0xE4,0x13,0x04,0x10,0x04,0x10,0xFC,0x1F,0x04,0x10,0x00,0x00},/*"回",8*/

{0x10,0x00,0x10,0x08,0x10,0x04,0x10,0x02,0x10,0x01,0x90,0x00,0x10,0x00,0xFF,0x7F,
0x50,0x00,0x90,0x00,0x10,0x01,0x10,0x02,0x90,0x04,0x50,0x18,0x30,0x60,0x10,0x00},/*"长",9*/

{0x04,0x00,0x84,0x7F,0x3C,0x00,0x0A,0x00,0x09,0x3F,0x08,0x21,0x08,0x21,0x7F,0x21,
0x08,0x3F,0x08,0x00,0x08,0x21,0x14,0x22,0x24,0x12,0x22,0x00,0x82,0x7F,0x01,0x00},/*"短",10*/

{0x08,0x02,0x08,0x04,0x08,0x04,0xC8,0x7F,0x5F,0x40,0x28,0x22,0x08,0x02,0xD8,0x7F,
0x0C,0x11,0x0B,0x11,0x88,0x10,0x08,0x0B,0x08,0x04,0x08,0x0A,0x0A,0x11,0xC4,0x20},/*"按",11*/

{0x00,0x00,0xE2,0x3F,0x24,0x22,0x24,0x22,0xA0,0x2F,0x20,0x22,0x27,0x22,0xE4,0x3F,
0x24,0x20,0xA4,0x2F,0xA4,0x28,0xB4,0x28,0xAC,0x2F,0x24,0x20,0x20,0x28,0x10,0x10},/*"调",12*/

{0x00,0x00,0xE2,0x1F,0x04,0x08,0x84,0x05,0x00,0x02,0xE0,0x3F,0x27,0x22,0x24,0x22,
0xE4,0x3F,0x24,0x22,0x24,0x22,0xE4,0x3F,0x24,0x22,0x24,0x2A,0x2A,0x10,0xF1,0x7F},/*"通",13*/

{0x08,0x00,0x88,0x7F,0x0A,0x04,0x3A,0x02,0x8A,0x3F,0x8A,0x20,0xFF,0x24,0x80,0x24,
0x88,0x24,0xAA,0x24,0xAA,0x24,0xAA,0x22,0x21,0x0A,0x10,0x11,0x8C,0x20,0x43,0x40},/*"频",14*/

{0x40,0x10,0x84,0x08,0x08,0x00,0xE8,0x3F,0x00,0x01,0xC0,0x1F,0x4F,0x10,0xC8,0x1F,
0x48,0x10,0xC8,0x1F,0x48,0x10,0xC8,0x1F,0x48,0x10,0x14,0x00,0xE2,0x7F,0x00,0x00},/*"道",15*/

{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFF,0x7F,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*"一",16*/

{0x08,0x00,0xC8,0x3F,0x04,0x21,0x04,0x11,0x12,0x11,0x1F,0x09,0x08,0x39,0x04,0x21,
0x82,0x22,0x9F,0x22,0x82,0x14,0x80,0x14,0x58,0x08,0x47,0x14,0x22,0x22,0x80,0x41},/*"级",17*/

{0x40,0x00,0x20,0x02,0x10,0x04,0xF8,0x0F,0x40,0x00,0xFE,0x3F,0x10,0x04,0x08,0x09,
0xC4,0x10,0x33,0x62,0x80,0x01,0x60,0x08,0x18,0x04,0x00,0x03,0xE0,0x00,0x1E,0x00},/*"参",18*/

{0x70,0x00,0x8F,0x3B,0x49,0x22,0x2A,0x22,0xFF,0x2A,0x1C,0x33,0x2A,0x22,0x41,0x22,
0x3E,0x32,0x2A,0x2B,0xAA,0x26,0x3E,0x22,0x2A,0x22,0x2A,0x22,0xBE,0x2A,0x22,0x11},/*"翻",19*/

{0x00,0x00,0xFE,0x3F,0x40,0x00,0x20,0x00,0xF8,0x0F,0x08,0x08,0x88,0x08,0x88,0x08,
0x88,0x08,0x88,0x08,0x88,0x08,0x48,0x09,0x40,0x02,0x20,0x04,0x18,0x08,0x06,0x10},/*"页",20*/

{0x00,0x09,0x04,0x09,0x08,0x09,0xC8,0x3F,0x00,0x09,0x00,0x09,0x0F,0x09,0xE8,0x7F,
0x08,0x09,0x08,0x09,0x88,0x08,0x88,0x08,0x48,0x08,0x14,0x00,0xE2,0x7F,0x00,0x00},/*"进",21*/

{0x20,0x00,0x40,0x00,0x80,0x00,0x80,0x00,0x80,0x00,0x40,0x01,0x40,0x01,0x40,0x01,
0x20,0x02,0x20,0x02,0x10,0x04,0x10,0x04,0x08,0x08,0x04,0x08,0x02,0x10,0x01,0x60},/*"入",22*/

{0x10,0x04,0x10,0x04,0xFF,0x7F,0x10,0x04,0x00,0x08,0x00,0x1F,0xFC,0x00,0x88,0x08,
0x10,0x04,0x80,0x00,0xFE,0x3F,0xA0,0x02,0x90,0x04,0x8C,0x18,0x83,0x60,0x80,0x00},/*"菜",23*/

{0x08,0x08,0x10,0x04,0x20,0x02,0xFC,0x1F,0x84,0x10,0x84,0x10,0xFC,0x1F,0x84,0x10,
0x84,0x10,0xFC,0x1F,0x80,0x00,0x80,0x00,0xFF,0x7F,0x80,0x00,0x80,0x00,0x80,0x00},/*"单",24*/

{0x08,0x04,0x88,0x24,0x3C,0x25,0x04,0x15,0x02,0x04,0xBD,0x3F,0x88,0x20,0x88,0x24,
0xBF,0x24,0x88,0x24,0x88,0x24,0x88,0x24,0x28,0x0A,0x18,0x11,0x88,0x20,0x40,0x40},/*"锁",25*/

{0x00,0x00,0xFC,0x1F,0x04,0x10,0x04,0x10,0xFC,0x1F,0x24,0x08,0x44,0x04,0xF4,0x1F,
0x44,0x04,0x44,0x04,0xFC,0x3F,0x44,0x04,0x42,0x04,0x22,0x04,0x21,0x04,0x10,0x04},/*"屏",26*/

{0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00},/*" ",27*/


};

// 16x16 点阵的英文字母字模数据
const uint8_t letter_font[26][16] = {
/*A(0) B(1) C(2) D(3) E(4) F(5) G(6) 
H(7) I(8) J(9) K(10) L(11) M(12) N(13) 
O(14) P(15) Q(16) R(17) S(18) T(19) U(20) 
V(21) W(22) S(23) Y(24) Z(25)*/

{0x00,0x00,0x00,0x08,0x08,0x18,0x14,0x14,0x24,0x3C,0x22,0x42,0x42,0xE7,0x00,0x00},/*"A",0*/

{0x00,0x00,0x00,0x1F,0x22,0x22,0x22,0x1E,0x22,0x42,0x42,0x42,0x22,0x1F,0x00,0x00},/*"B",1*/

{0x00,0x00,0x00,0x7C,0x42,0x42,0x01,0x01,0x01,0x01,0x01,0x42,0x22,0x1C,0x00,0x00},/*"C",2*/

{0x00,0x00,0x00,0x1F,0x22,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x22,0x1F,0x00,0x00},/*"D",3*/

{0x00,0x00,0x00,0x3F,0x42,0x12,0x12,0x1E,0x12,0x12,0x02,0x42,0x42,0x3F,0x00,0x00},/*"E",4*/

{0x00,0x00,0x00,0x3F,0x42,0x12,0x12,0x1E,0x12,0x12,0x02,0x02,0x02,0x07,0x00,0x00},/*"F",5*/

{0x00,0x00,0x00,0x3C,0x22,0x22,0x01,0x01,0x01,0x71,0x21,0x22,0x22,0x1C,0x00,0x00},/*"G",6*/

{0x00,0x00,0x00,0xE7,0x42,0x42,0x42,0x42,0x7E,0x42,0x42,0x42,0x42,0xE7,0x00,0x00},/*"H",7*/

{0x00,0x00,0x00,0x3E,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x3E,0x00,0x00},/*"I",8*/

{0x00,0x00,0x00,0x7C,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x10,0x11,0x0F},/*"J",9*/

{0x00,0x00,0x00,0x77,0x22,0x12,0x0A,0x0E,0x0A,0x12,0x12,0x22,0x22,0x77,0x00,0x00},/*"K",10*/

{0x00,0x00,0x00,0x07,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x02,0x42,0x7F,0x00,0x00},/*"L",11*/

{0x00,0x00,0x00,0x77,0x36,0x36,0x36,0x36,0x36,0x2A,0x2A,0x2A,0x2A,0x6B,0x00,0x00},/*"M",12*/

{0x00,0x00,0x00,0xE3,0x46,0x46,0x4A,0x4A,0x52,0x52,0x52,0x62,0x62,0x47,0x00,0x00},/*"N",13*/

{0x00,0x00,0x00,0x1C,0x22,0x41,0x41,0x41,0x41,0x41,0x41,0x41,0x22,0x1C,0x00,0x00},/*"O",14*/

{0x00,0x00,0x00,0x3F,0x42,0x42,0x42,0x42,0x3E,0x02,0x02,0x02,0x02,0x07,0x00,0x00},/*"P",15*/

{0x00,0x00,0x00,0x1C,0x22,0x41,0x41,0x41,0x41,0x41,0x41,0x4D,0x32,0x1C,0x60,0x00},/*"Q",16*/

{0x00,0x00,0x00,0x3F,0x42,0x42,0x42,0x3E,0x12,0x12,0x22,0x22,0x42,0xC7,0x00,0x00},/*"R",17*/

{0x00,0x00,0x00,0x7C,0x42,0x42,0x02,0x04,0x18,0x20,0x40,0x42,0x42,0x3E,0x00,0x00},/*"S",18*/

{0x00,0x00,0x00,0x7F,0x49,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x08,0x1C,0x00,0x00},/*"T",19*/

{0x00,0x00,0x00,0xE7,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x42,0x3C,0x00,0x00},/*"U",20*/

{0x00,0x00,0x00,0xE7,0x42,0x42,0x22,0x24,0x24,0x14,0x14,0x18,0x08,0x08,0x00,0x00},/*"V",21*/

{0x00,0x00,0x00,0x6B,0x2A,0x2A,0x2A,0x2A,0x2A,0x36,0x14,0x14,0x14,0x14,0x00,0x00},/*"W",22*/

{0x00,0x00,0x00,0x7C,0x42,0x42,0x02,0x04,0x18,0x20,0x40,0x42,0x42,0x3E,0x00,0x00},/*"S",23*/

{0x00,0x00,0x00,0x77,0x22,0x22,0x14,0x14,0x08,0x08,0x08,0x08,0x08,0x1C,0x00,0x00},/*"Y",24*/

{0x00,0x00,0x00,0x7E,0x21,0x20,0x10,0x10,0x08,0x04,0x04,0x42,0x42,0x3F,0x00,0x00},/*"Z",25*/


};



