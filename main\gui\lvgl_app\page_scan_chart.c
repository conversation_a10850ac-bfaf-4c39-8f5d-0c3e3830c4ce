#include "page_scan.h"
#include "page_scan_chart.h"
#include "page_menu.h"
#include "page_main.h"
#include "rx5808.h"
#include "rx5808_config.h"
#include "lvgl_stl.h"
#include "lv_port_disp.h"
#include "beep.h"
#include "page_menu.h"
#include "osd_font.h"

#define page_scan_chart_anim_enter  lv_anim_path_bounce
#define page_scan_chart_anim_leave  lv_anim_path_bounce

#define scan_turn_time  90

const uint16_t Rx5808_Freq_scan_chart[11][8]=
{  
    {4920, 4940, 4960, 4990, 4990, 5020, 5050, 5080}, 
    {5110, 5140, 5170, 5200, 5325, 5333, 5348, 5366}, 
    {5373, 5384, 5402, 5413, 5420, 5438, 5453, 5456}, 
    {5474, 5492, 5493, 5510, 5528, 5533, 5546, 5564}, 
    {5573, 5582, 5600, 5613, 5645, 5653, 5658, 5665},//40

    {5685, 5693, 5695, 5705, 5725, 5732, 5732, 5733},//41
    {5740, 5745, 5752, 5760, 5765, 5769, 5771, 5773}, 
    {5780, 5785, 5790, 5800, 5800, 5805, 5806, 5809}, 
    {5813, 5820, 5825, 5828, 5840, 5843, 5845, 5847}, 
    {5853, 5860, 5865, 5866, 5880, 5880, 5885, 5905}, 
    {5917, 5925, 5933, 5945, 5965, 5985, 6000, 6010},
};

static lv_obj_t* page_scan_chart_contain = NULL;
static lv_obj_t* chart_fre_label;
static lv_group_t* scan_group;
static uint8_t time_repeat_count = 0;
static lv_obj_t* rssi_quality_chart;
static lv_chart_series_t* rssi0_curve;
static lv_chart_series_t* rssi1_curve;
static lv_timer_t* scan_chart_timer;
static uint8_t  max_rssi_chart;
static uint8_t  max_channel_chart;
static lv_obj_t* fre_info_label;

// static const int fre_channel_num[] = { 1,2,3,4,5,6,7,8 };

uint8_t band, channel; 
uint16_t ferq_old;
uint8_t max_channel_chart_new = 0;
uint8_t scan_chart_cont = 125;

static void page_scan_chart_timer_event(lv_timer_t* tmr);
static void page_scan_event_callback(lv_event_t* event);

lv_timer_t *page_scan_chart_time;
lv_indev_state_t key_state_scan_chart = LV_INDEV_STATE_RELEASED;

static void page_scan_chart_time_callback(lv_timer_t *tmr) {  
    if (tmr == page_scan_chart_time)
    {
        lv_timer_reset(page_scan_chart_time); 
        lv_timer_del(page_scan_chart_time); 
        page_scan_chart_exit();
        key_delay_time_scan(800);
        video_composite_switch(false); 
    }
    key_state_scan_chart = LV_INDEV_STATE_RELEASED;    
}

void key_delay_time_scan_chart(uint32_t time_out) 
{
    page_scan_chart_time = lv_timer_create(page_scan_chart_time_callback, time_out, NULL);   
    lv_indev_t *indev = lv_indev_get_act();   
    if (indev) {
        key_state_scan_chart = lv_indev_get_key(indev);  
        if (key_state_scan_chart == LV_INDEV_STATE_PRESSED) {
            lv_timer_set_repeat_count(page_scan_chart_time, 1);
        }
    }
    
}

static void show_osd_tips(void)
{
    if(lock_flag == false) return;

    clear_operation_tip_area(0, 0, 10, 3);  // 320 * 96

    if (RX5808_Get_Language() == 0)
    {
        draw_string(10, 8 ,"RETURN LEFT OR RIGHT");
    }
    else
    {
        // 左右返回
        draw_hanzi(10, 10, 3);
        draw_hanzi(26, 10, 4);
        draw_hanzi(42, 10, 7);
        draw_hanzi(58, 10, 8);
    }
}

static void page_scan_chart_timer_event(lv_timer_t* tmr)
{

    int repeat_count = 128 - tmr->repeat_count;
    time_repeat_count = repeat_count;
    if (tmr == scan_chart_timer)
    {
        // 信号强度获取和处理
        uint8_t rssi_pre_chart = 0;
        if(RX5808_Get_Signal_Source()==1)
		{					
            rssi_pre_chart = Rx5808_Get_Precentage1();    // 得到的是最好的信号
            lv_chart_set_value_by_id(rssi_quality_chart, rssi1_curve, repeat_count, Rx5808_Get_Precentage1());
		}
		else if(RX5808_Get_Signal_Source()==2)
		{
            rssi_pre_chart = Rx5808_Get_Precentage0();
            lv_chart_set_value_by_id(rssi_quality_chart, rssi0_curve, repeat_count, Rx5808_Get_Precentage0());
		}
		else
		{
            rssi_pre_chart = (Rx5808_Get_Precentage0() + Rx5808_Get_Precentage1()) / 2;
		    lv_chart_set_value_by_id(rssi_quality_chart, rssi0_curve, repeat_count, Rx5808_Get_Precentage0());
            lv_chart_set_value_by_id(rssi_quality_chart, rssi1_curve, repeat_count, Rx5808_Get_Precentage1());
		}    

        if (rssi_pre_chart > max_rssi_chart)
        {
            max_rssi_chart = rssi_pre_chart;
            int max_adjusted_count = time_repeat_count;
            if(time_repeat_count < 15){
                scan_chart_cont = 122;
            }
            else{
                scan_chart_cont = 127;
            }
            if(max_adjusted_count < 80){
                if(max_adjusted_count < 6){
                    max_adjusted_count = 5;
                }
                else{
                    max_adjusted_count = (max_adjusted_count ) / 2 ;
                }
    
            }
            else{
                max_adjusted_count = max_adjusted_count - 41;
            }
            max_channel_chart = max_adjusted_count;
            
        }    

        // 标签文本设置和位置调整
        // lv_label_set_text_fmt(fre_info_label, "%d", fre_channel_num[repeat_count % 8]);
        lv_obj_set_pos(fre_info_label, -2, 0);
        lv_obj_set_style_border_width(fre_info_label, 2, LV_STATE_DEFAULT);
        lv_label_set_long_mode(fre_info_label, LV_LABEL_LONG_WRAP);
        // lv_label_set_text_fmt(fre_info_label, "%c%d:%d", Rx5808_ChxMap[time_repeat_count / 8], (time_repeat_count % 8) + 1, Rx5808_Freq[repeat_count / 8][repeat_count % 8]);
        lv_label_set_text_fmt(fre_info_label, "MAX CH");
        

        if (time_repeat_count < scan_chart_cont){   
            int adjusted_count = time_repeat_count;
            // if(adjusted_count < 0){
            //     adjusted_count = 1;
            // }
            if(adjusted_count < 80){
                if((adjusted_count & 1) != 0){ // 奇数
                    adjusted_count = (adjusted_count + 1) / 2 - 1;
                }
                else if((adjusted_count & 1) == 0){   // 偶数
                    adjusted_count = (adjusted_count ) / 2 - 1;
                }
            }
            else{
                adjusted_count = adjusted_count - 40;
            }

            RX5808_Set_Freq(Rx5808_Freq_scan_chart[(adjusted_count + 1) / 8][(adjusted_count + 1) % 8]);
            lv_label_set_text_fmt(fre_info_label, "%d:%d", (adjusted_count + 1) / 8, (adjusted_count + 1) % 8);

        }
        else if (time_repeat_count == 128)
        { 
            ferq_old = Rx5808_Freq_scan_chart[max_channel_chart / 8][max_channel_chart % 8];

            max_channel_chart_new = 0;
            for(band = 0 ; band < 10; band++)
            {
                for(channel = 0; channel < 8; channel++ )
                {
                    if(ferq_old == Rx5808_Freq[band][channel])
                    {
                        max_channel_chart_new = band * 8 + channel;
                        break;
                    }
                }
                if(max_channel_chart_new != 0) break;
            } 
            RX5808_Set_Freq(Rx5808_Freq[Chx_count][channel_count]);
            rx5808_div_setup_upload(rx5808_div_config_channel); 
            lv_label_set_text_fmt(fre_info_label, "%c%d:%d", Rx5808_ChxMap[max_channel_chart_new / 8], (max_channel_chart_new % 8) + 1, Rx5808_Freq[max_channel_chart_new / 8][max_channel_chart_new % 8]);

        }
    }
}

static void page_scan_event_callback(lv_event_t* event)
{
    lv_event_code_t code = lv_event_get_code(event);
    lv_obj_t* obj = lv_event_get_target(event);
    if (code == LV_EVENT_KEY)
    {
        beep_on_off(beep_get_status());
        lv_fun_param_delayed(beep_on_off, 100, 0);
        lv_key_t key_status = lv_indev_get_key(lv_indev_get_act());
        if (key_status == LV_KEY_LEFT) {
            page_scan_chart_exit();
        }
        else if(key_status == LV_KEY_RIGHT){
            page_scan_chart_exit();
        }
        else if (key_status == LV_KEY_UP) {}
        else if (key_status == LV_KEY_DOWN) {} 
    }
}

void page_scan_chart_exit()
{

    lv_amin_start(rssi_quality_chart, lv_obj_get_y(rssi_quality_chart), -60, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_scan_chart_anim_leave);
    lv_amin_start(fre_info_label, lv_obj_get_y(fre_info_label), -60, 1, 200, 300, (lv_anim_exec_xcb_t)lv_obj_set_y, page_scan_chart_anim_leave);
    lv_amin_start(chart_fre_label, lv_obj_get_y(chart_fre_label), 80, 1, 200, 300, (lv_anim_exec_xcb_t)lv_obj_set_y, page_scan_chart_anim_leave);
    if (time_repeat_count < 128)
    {
        lv_timer_del(scan_chart_timer);
        RX5808_Set_Freq(Rx5808_Freq[Chx_count][channel_count]);
        // RX5808_Set_Freq(Rx5808_Freq[max_channel / 8][max_channel % 8]);
    }

    lv_obj_del_delayed(page_scan_chart_contain, 500);
    // lv_fun_delayed(page_scan_create, 500);
    lv_fun_param_delayed(page_menu_create, 500, item_scan);
    lv_group_del(scan_group);
}

void page_scan_chart_create()
{
    show_osd_tips();
    lv_color_t chart_bg_color = lock_flag?lv_color_black():lv_color_make(100, 100, 100);
    time_repeat_count = 0;
    max_rssi_chart = 0;
    page_scan_chart_contain = lv_obj_create(lv_scr_act());
    lv_obj_remove_style_all(page_scan_chart_contain);
    lv_obj_set_style_bg_color(page_scan_chart_contain, lv_color_make(0, 0, 0), LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(page_scan_chart_contain, (lv_opa_t)LV_OPA_COVER, LV_STATE_DEFAULT);
    lv_obj_set_size(page_scan_chart_contain, 160, 80);
    lv_obj_set_pos(page_scan_chart_contain, 0, 0);


    rssi_quality_chart = lv_chart_create(page_scan_chart_contain);
    lv_obj_remove_style(rssi_quality_chart, NULL, LV_PART_MAIN);
    lv_obj_set_style_bg_opa(rssi_quality_chart, (lv_opa_t)LV_OPA_TRANSP, LV_STATE_DEFAULT);
    lv_obj_set_size(rssi_quality_chart, 145, 60);
    lv_obj_set_pos(rssi_quality_chart, 10, 5);
    lv_chart_set_type(rssi_quality_chart, LV_CHART_TYPE_LINE);   /*Show lines and points too*/
    lv_obj_set_style_text_color(rssi_quality_chart, lv_color_white(), LV_STATE_DEFAULT);
    lv_chart_set_range(rssi_quality_chart, LV_CHART_AXIS_PRIMARY_Y, 0, 110);
    lv_chart_set_range(rssi_quality_chart, LV_CHART_AXIS_PRIMARY_X, 0, 128);

    lv_obj_set_style_border_color(rssi_quality_chart, lv_color_make(0, 0, 0), LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(rssi_quality_chart, chart_bg_color, LV_STATE_DEFAULT);
    lv_obj_set_style_bg_opa(rssi_quality_chart, 100, LV_STATE_DEFAULT);
    //lv_obj_set_style_text_color(rssi_quality_chart, lv_color_make(0, 0, 255), LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(rssi_quality_chart, 0, LV_STATE_DEFAULT);
    //lv_obj_set_style_text_color_filtered(rssi_quality_chart, lv_color_make(0, 0, 255), LV_STATE_DEFAULT);
    //lv_obj_set_style_text_font(rssi_quality_chart, &lv_font_montserrat_8,LV_STATE_DEFAULT);


    lv_obj_set_style_size(rssi_quality_chart, 0, LV_PART_INDICATOR);
    lv_chart_set_div_line_count(rssi_quality_chart, 5 ,  128);   // 设置垂直和水平分割的数量
    //lv_chart_set_range(rssi_quality_chart, LV_CHART_AXIS_SECONDARY_Y, -50, 100);
    lv_chart_set_point_count(rssi_quality_chart, 128);    // 80 个点连成直线
    lv_chart_set_axis_tick(rssi_quality_chart, LV_CHART_AXIS_PRIMARY_X, 5, 3, 7, 1, true, 20);
    lv_chart_set_axis_tick(rssi_quality_chart, LV_CHART_AXIS_PRIMARY_Y, 5, 3, 5, 2, true, 50);

    // 添加一个数据系列
    if(RX5808_Get_Signal_Source()==1)
    {						
        rssi1_curve = lv_chart_add_series(rssi_quality_chart, lv_palette_main(LV_PALETTE_BLUE), LV_CHART_AXIS_PRIMARY_Y);
    }
    else if(RX5808_Get_Signal_Source()==2)
    {
        rssi0_curve = lv_chart_add_series(rssi_quality_chart, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);
    }
    else
    {
        rssi0_curve = lv_chart_add_series(rssi_quality_chart, lv_palette_main(LV_PALETTE_RED), LV_CHART_AXIS_PRIMARY_Y);
        rssi1_curve = lv_chart_add_series(rssi_quality_chart, lv_palette_main(LV_PALETTE_BLUE), LV_CHART_AXIS_PRIMARY_Y);
    }


    chart_fre_label = lv_label_create(page_scan_chart_contain);

    lv_obj_set_style_text_font(chart_fre_label, &lv_font_montserrat_12, LV_STATE_DEFAULT);  // 这里是显示字体大小
    lv_obj_set_style_text_color(chart_fre_label, lv_color_make(255, 255, 255), LV_STATE_DEFAULT);
    lv_obj_set_style_text_opa(chart_fre_label, 255, LV_STATE_DEFAULT);
    lv_obj_set_style_bg_color(chart_fre_label, lv_color_make(0, 0, 0), LV_STATE_DEFAULT);
    // lv_label_set_text_fmt(chart_fre_label, "5300   5450   5600   5750   5900");
    lv_label_set_text_fmt(chart_fre_label, "5000   5330   5660   6000");
    lv_obj_set_pos(chart_fre_label, 10, 68);

    fre_info_label = lv_label_create(page_scan_chart_contain);    
    //lv_obj_set_pos(scan_info_label, 0, 0);
    lv_obj_align(fre_info_label, LV_ALIGN_TOP_RIGHT, -2, 2);
    lv_obj_set_style_bg_color(fre_info_label, lv_color_make(0, 0, 0), LV_STATE_DEFAULT);
    lv_obj_set_style_border_color(fre_info_label, lv_color_make(0x00, 0x00, 0x00), LV_STATE_DEFAULT);
    lv_obj_set_style_text_font(fre_info_label, &lv_font_montserrat_16, LV_STATE_DEFAULT);
    lv_obj_set_style_text_color(fre_info_label, lv_color_make(255, 128, 255), LV_STATE_DEFAULT);
    lv_label_set_long_mode(fre_info_label, LV_LABEL_LONG_WRAP);


    scan_group = lv_group_create();
    lv_indev_set_group(indev_keypad, scan_group);


    lv_obj_add_event_cb(rssi_quality_chart, page_scan_event_callback, LV_EVENT_KEY, NULL);

    lv_group_add_obj(scan_group, rssi_quality_chart);
    lv_group_set_editing(scan_group, false);

    RX5808_Set_Freq(Rx5808_Freq_scan_chart[0][0]);
    scan_chart_timer = lv_timer_create(page_scan_chart_timer_event, scan_turn_time, NULL);
    lv_timer_set_repeat_count(scan_chart_timer, 128);

    lv_amin_start(rssi_quality_chart, -60, 5, 1, 500, 0, (lv_anim_exec_xcb_t)lv_obj_set_y, page_scan_chart_anim_enter);
    lv_amin_start(fre_info_label, -60, 5, 1, 200, 300, (lv_anim_exec_xcb_t)lv_obj_set_y, page_scan_chart_anim_enter);
    lv_amin_start(chart_fre_label, 80, 68, 1, 200, 200, (lv_anim_exec_xcb_t)lv_obj_set_y, page_scan_chart_anim_enter);
}



