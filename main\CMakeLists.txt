file(GLOB_RECURSE SOURCES "./*.c"
    "./gui/lvgl/src/*.c"
    "./hardware/*.c"
    "./gui/lvgl_app/*.c"
    "./gui/lvgl/src/core/*.c"
    "./gui/lvgl/src/draw/*.c"
    "./gui/lvgl/src/extra/*.c"
    "./gui/lvgl/src/font/*.c"
    "./gui/lvgl/src/gpu/*.c"
    "./gui/lvgl/src/hal/*.c"
    "./gui/lvgl/src/misc/*.c"
    "./gui/lvgl/src/widgets/*.c"
    "./gui/lvgl/src/extra/layouts/*.c"
    "./gui/lvgl/src/extra/layouts/flex/*.c"
    "./gui/lvgl/src/extra/layouts/grid/*.c"
    "./gui/lvgl/src/extra/themes/*.c"
    "./gui/lvgl/src/extra/themes/basic/*.c"
    "./gui/lvgl/src/extra/themes/default/*.c"
    "./gui/lvgl/src/extra/widgets/calendar/*.c"
    "./gui/lvgl/src/extra/widgets/colorwheel/*.c"
    "./gui/lvgl/src/extra/widgets/*.c"
    "./gui/lvgl/src/extra/widgets/imgbtn/*.c"
    "./gui/lvgl/src/extra/widgets/keyboard/*.c"
    "./gui/lvgl/src/extra/widgets/led/*.c"
    "./gui/lvgl/src/extra/widgets/list/*.c"
    "./gui/lvgl/src/extra/widgets/msgbox/*.c"
    "./gui/lvgl/src/extra/widgets/spinbox/*.c"
    "./gui/lvgl/src/extra/widgets/spinner/*.c"
    "./gui/lvgl/src/extra/widgets/tabview/*.c"
    "./gui/lvgl/src/extra/widgets/tileview/*.c"
    "./gui/lvgl/src/extra/widgets/win/*.c"
    "./gui/lvgl_app/*.c"
    "./gui/lvgl_app/resources/*.c"
    "./gui/lvgl_driver/*.c"
    "./driver/*.c"
)
set(INCLUDE_DIRSx "./"
    "./hardware/"
    "./gui/"
    "./gui/lvgl/"
    "./gui/lvgl/src/"
    "./gui/lvgl/src/core/"
    "./gui/lvgl/src/draw/"
    "./gui/lvgl/src/extra/"
    "./gui/lvgl/src/font/"
    "./gui/lvgl/src/gpu/"
    "./gui/lvgl/src/hal/"
    "./gui/lvgl/src/misc/"
    "./gui/lvgl/src/widgets/"
    "./gui/lvgl/src/extra/layouts/"
    "./gui/lvgl/src/extra/layouts/flex/"
    "./gui/lvgl/src/extra/layouts/grid/"
    "./gui/lvgl/src/extra/themes/"
    "./gui/lvgl/src/extra/themes/basic/"
    "./gui/lvgl/src/extra/themes/default/"
    "./gui/lvgl/src/extra/widgets/calendar/"
    "./gui/lvgl/src/extra/widgets/colorwheel/"
    "./gui/lvgl/src/extra/widgets/"
    "./gui/lvgl/src/extra/widgets/imgbtn/"
    "./gui/lvgl/src/extra/widgets/keyboard/"
    "./gui/lvgl/src/extra/widgets/led/"
    "./gui/lvgl/src/extra/widgets/list/"
    "./gui/lvgl/src/extra/widgets/msgbox/"
    "./gui/lvgl/src/extra/widgets/spinbox/"
    "./gui/lvgl/src/extra/widgets/spinner/"
    "./gui/lvgl/src/extra/widgets/tabview/"
    "./gui/lvgl/src/extra/widgets/tileview/"
    "./gui/lvgl/src/extra/widgets/win/"
    "./gui/lvgl_app/"
    "./gui/lvgl_app/resources/"
    "./gui/lvgl_driver/"
    "./driver/"
)

set(SRCSx 
    main.c
    hardware/msp.c
)

idf_component_register(SRCS ${SRCSx} ${SOURCES} INCLUDE_DIRS ${INCLUDE_DIRSx})

# idf_component_register(SRCS "main.c" INCLUDE_DIRS "")
